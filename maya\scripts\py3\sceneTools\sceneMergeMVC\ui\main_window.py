import maya.OpenMayaUI as omui
import maya.cmds as cmds
import traceback
from .. import *

from .styles import load_main_style
from ..ui.widgets.outliner_panel import OutlinerPanel
from ..ui.widgets.drag_drop_panel import DragDropPanel
from ..ui.widgets.menu import MainMenu
# from ..core import maya_utils
from ..core import maya_utils as Utils
from ..core.scene_merger import SceneMerger


def get_maya_main_window():
	"""Get Maya main window as QWidget."""
	main_window_ptr = omui.MQtUtil.mainWindow()
	if main_window_ptr:
		return wrapInstance(int(main_window_ptr), QWidget)
	return None


class MergeScenesWindow(QDialog):
	"""
	Main dialog window for the Merge Scenes tool.
	"""

	def __init__(self, parent=None):
		"""
		Class initializer.
		"""
		super(MergeScenesWindow, self).__init__(parent or get_maya_main_window())
		self.setObjectName("MergeScenesMainWindow")
		main_style = load_main_style()
		self.setStyleSheet(main_style)

		self.settings = QSettings("GameStudio", "MergeScenesTool")
		self.setWindowTitle("Merge Scenes Tool MVP v0.3")

		# Initialize override mode
		self.current_override_mode = None

		self.init_ui()
		self.load_settings()

	def init_ui(self):
		"""Creates and layouts all window widgets."""
		self.create_widgets()
		self.create_layouts()
		self.connect_signals()

	def create_widgets(self):
		"""Creates all necessary widgets."""
		self.splitter = QSplitter(Qt.Horizontal)
		self.splitter.setObjectName("MainSplitterMergeScenes")

		self.reference_panel = DragDropPanel()
		self.outliner_panel = OutlinerPanel()

		self.splitter.addWidget(self.reference_panel)
		self.splitter.addWidget(self.outliner_panel)

		self.merge_all_btn = QPushButton("Merge All")
		self.merge_valid_btn = QPushButton("Merge Valid")
		self.merge_selected_btn = QPushButton("Merge Selected")
		self.delete_refs_btn = QPushButton("Delete References")

		self.menu_bar = MainMenu(self)

	def create_layouts(self):
		"""Creates layouts and places widgets in them."""
		button_layout = QHBoxLayout()
		button_layout.addStretch()
		button_layout.addWidget(self.merge_all_btn)
		button_layout.addWidget(self.merge_valid_btn)
		button_layout.addWidget(self.merge_selected_btn)
		button_layout.addWidget(self.delete_refs_btn)

		main_layout = QVBoxLayout(self)
		main_layout.setContentsMargins(6, 3, 6, 8)
		main_layout.setMenuBar(self.menu_bar)
		main_layout.addWidget(self.splitter)
		main_layout.addLayout(button_layout)

	def connect_signals(self):
		"""Connects widget signals to methods (slots)."""
		self.merge_all_btn.clicked.connect(self.on_merge_all_clicked)
		self.merge_valid_btn.clicked.connect(self.on_merge_valid_clicked)
		# self.merge_valid_btn.clicked.connect(lambda: self.on_validate_mode_changed(True))
		self.merge_selected_btn.clicked.connect(self.on_merge_selected_clicked)
		self.delete_refs_btn.clicked.connect(self.on_delete_references_clicked)

		# Drag and Drop Panel
		self.reference_panel.file_validated.connect(self.on_file_loaded)

		# Outliner Panel
		self.outliner_panel.validation_toggled.connect(self.on_validate_mode_changed)

	def on_validate_mode_changed(self, checked):
		print("\nController: Validation mode changed to:\n")
		print("\nController: value of checked is:\n", checked)

		if checked:
			Utils.analyze_and_color(self.outliner_panel.outliner_panel)
		else:
			# Utils.remove_validation_colors(self.outliner_panel.outliner_panel)
			print("\nController: Validation colors removed from outliner.\n")

	def on_override_mode_changed(self, mode):
		"""
		Slot to update the current override mode.
		Called by a signal from the toolbar.
		"""
		self.current_override_mode = mode
		print(f"Controller: Override mode changed to: {self.current_override_mode}")

		if hasattr(self, 'merger'):
			self.merger.set_override_mode(mode)
			print(f"Controller: Mode passed to SceneMerger")

	def on_file_loaded(self, file_path):
		"""
		This slot method is called when DragDropPanel emits the file_validated signal.
		"""
		current_scene_path = Utils.get_current_scene_path()
		if current_scene_path and os.path.normpath(current_scene_path) == os.path.normpath(file_path):
			print(f"Controller: Attempted to load the currently open scene: {file_path}")
			self.reference_panel.display_placeholder()
			self.reference_panel.placeholder_widget.set_error_state(
				"Cannot load the currently open scene as a reference.")
			return

		print(f"Controller: Received request to load file: {file_path}")

		print("Controller: Clearing old references...")
		Utils.delete_reference()

		print(f"Controller: Loading new reference '{os.path.basename(file_path)}'...")
		try:
			Utils.load_reference(file_path)
			print("Controller: Updating UI to display outliner...")
			ref_outliner_panel = self.reference_panel.display_outliner(file_path)

			self.merger = SceneMerger(namespace="r", outliner_panel=ref_outliner_panel.outliner_panel)
			self.merger.set_override_mode(self.current_override_mode)
			self.merger.analyze_and_color()
		except Exception as e:
			print(f"Controller: Error loading reference: {e}")
			self.reference_panel.display_placeholder()
			self.reference_panel.placeholder_widget.set_error_state(
				f"Failed to load:\n{os.path.basename(file_path)}")
			traceback.print_exc()

	def on_delete_references_clicked(self):
		"""Handles 'Delete References' button click."""
		print("Controller: Received request to delete references.")
		Utils.delete_reference()
		self.reference_panel.display_placeholder()

	def on_merge_all_clicked(self):
		"""Handles 'Merge All' button click."""
		if not hasattr(self, 'merger'):
			cmds.warning("Please load a reference first.")
			return
		print("Controller: Merging all objects...")
		if self.merger.merge_all():
			print("Controller: Merge completed successfully. Deleting reference...")
			Utils.delete_reference()
			self.reference_panel.display_placeholder()
		else:
			cmds.warning("Controller: Error merging all objects.")

	def on_merge_valid_clicked(self):
		"""Handles 'Merge Valid' button click."""
		if not hasattr(self, 'merger'):
			cmds.warning("Please load a reference first.")
			return

		if not self.current_override_mode:
			cmds.warning("Override mode is enabled but no sub-mode (Group or Mesh) is selected.")
			return

		print(f"Controller: Merging valid objects with mode: {self.current_override_mode}")
		if self.merger.merge_valid():
			print("Controller: Merge completed successfully. Deleting reference...")
		else:
			cmds.warning("Controller: Error merging valid objects.")

	def on_merge_selected_clicked(self):
		"""Handles 'Merge Selected' button click."""
		if not hasattr(self, 'merger'):
			cmds.warning("Please load a reference first.")
			return

		if not self.current_override_mode:
			cmds.warning("Override mode is enabled but no sub-mode (Group or Mesh) is selected.")
			return

		print(f"Controller: Merging selected objects with mode: {self.current_override_mode}")
		if self.merger.merge_selected():
			print("Controller: Selected objects merged successfully.")
			self.merger.analyze_and_color()
		else:
			cmds.warning("Controller: Error merging selected objects.")

	def load_settings(self):
		"""Loads window position and splitter state from QSettings."""
		print("Loading window settings...")
		geometry = self.settings.value("geometry")
		if geometry and isinstance(geometry, QByteArray):
			self.restoreGeometry(geometry)

		splitter_state = self.settings.value("splitterState")
		if splitter_state and isinstance(splitter_state, QByteArray):
			self.splitter.restoreState(splitter_state)

	def save_settings(self):
		"""Saves current window position and splitter state."""
		print("Saving window settings...")
		self.settings.setValue("geometry", self.saveGeometry())
		self.settings.setValue("splitterState", self.splitter.saveState())

	def closeEvent(self, event):
		"""
		Intercepts window close event to save settings.
		"""
		left_outliner = self.reference_panel.get_active_outliner()
		if left_outliner:
			left_outliner.delete_panel()

		self.outliner_panel.delete_panel()
		Utils.delete_reference()

		self.save_settings()
		super().closeEvent(event)
