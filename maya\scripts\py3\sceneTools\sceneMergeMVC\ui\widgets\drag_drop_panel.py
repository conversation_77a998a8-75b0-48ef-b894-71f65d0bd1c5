import os
import traceback
from ... import *
from .outliner_panel import OutlinerPanel  # imported new widgets
from ..styles import (  # Import styles
	get_placeholder_widget_style,
	get_placeholder_widget_error_style,
	get_placeholder_widget_drag_active_style
)


class PlaceholderWidget(QLabel):
	"""
	A placeholder widget that accepts drag-and-drop, paste, and click actions.
	In the MVC pattern, this class is part of the VIEW.
	Its only task is to capture user input (file path) and communicate it through a signal.
	It should not contain application logic.
	"""
	# Signal that is sent when a valid file path is received.
	# The controller will listen to this signal.
	file_validated = Signal(str)

	def __init__(self, parent=None):
		super(PlaceholderWidget, self).__init__(parent)
		self.setObjectName("placeholder-widget")
		self._setup_ui()
		self.setAcceptDrops(True)
		self.setFocusPolicy(Qt.StrongFocus)

	def _setup_ui(self):
		"""Sets up the initial appearance of the widget."""
		self.setAlignment(Qt.AlignCenter)
		self.reset_state()  # Set default text and style

	def reset_state(self):
		"""Resets the widget to its initial state."""
		self.setText("Drag / Paste / Click\nto load .mb/.ma/.fbx")
		self.setStyleSheet(get_placeholder_widget_style())

	def set_error_state(self, message):
		"""Updates the widget to display an error with auto-reset."""
		self.setText(f"✗ Error:\n{message}")
		self.setStyleSheet(get_placeholder_widget_error_style())
		# Start a timer to reset the state after 5 seconds
		QTimer.singleShot(5000, self.reset_state)

	# --- Private methods and event handlers ---

	def _handle_file_path(self, file_path):
		"""
		Validates the file path and emits a signal if it's correct.
		This is the only action the widget performs.
		"""
		if self._is_supported_file(file_path):
			print(f"View (PlaceholderWidget): Received supported file: {file_path}")
			# Notify the controller that a valid file has been received.
			self.file_validated.emit(file_path)
		else:
			self.set_error_state(f"File not supported:\n{os.path.basename(file_path)}")

	def _is_supported_file(self, file_path):
		"""Checks if the file is a supported format."""
		if not file_path or not os.path.isfile(file_path):
			return False
		supported_extensions = ['.mb', '.ma', '.fbx']
		return any(file_path.lower().endswith(ext) for ext in supported_extensions)

	def dragEnterEvent(self, event: QDragEnterEvent):
		if event.mimeData().hasUrls():
			file_path = event.mimeData().urls()[0].toLocalFile()
			if self._is_supported_file(file_path):
				# Just a visual change, doesn't affect logic
				self.setStyleSheet(get_placeholder_widget_drag_active_style())
				event.acceptProposedAction()

	def dragLeaveEvent(self, event):
		self.reset_state()
		super().dragLeaveEvent(event)

	def dropEvent(self, event: QDropEvent):
		self.reset_state()
		file_path = event.mimeData().urls()[0].toLocalFile()
		self._handle_file_path(file_path)
		event.acceptProposedAction()

	def mousePressEvent(self, event: QMouseEvent):
		if event.button() == Qt.LeftButton:
			file_path, _ = QFileDialog.getOpenFileName(
				self, "Select Scene File", "", "Maya Files (*.ma *.mb *.fbx)"
			)
			if file_path:
				self._handle_file_path(file_path)
		super().mousePressEvent(event)

	def keyPressEvent(self, event: QKeyEvent):
		if event.key() == Qt.Key_V and event.modifiers() == Qt.ControlModifier:
			clipboard = QApplication.clipboard()
			if clipboard.mimeData().hasUrls():
				file_path = clipboard.mimeData().urls()[0].toLocalFile()
				self._handle_file_path(file_path)
				event.accept()
		super().keyPressEvent(event)


class DragDropPanel(QWidget):
	"""
	Left panel that contains either a loading placeholder or an outliner.
	In the MVC pattern, this is a VIEW-CONTAINER.
	It doesn't decide which widget to show.
	It provides methods (`display_placeholder`, `display_outliner`)
	that the Controller will call to manage its content.
	"""
	# Forward the signal from the child widget up to the Controller
	file_validated = Signal(str)

	def __init__(self, parent=None):
		super(DragDropPanel, self).__init__(parent)
		self.setObjectName("DragDropPanel")
		self.layout = QVBoxLayout(self)
		self.layout.setContentsMargins(0, 0, 0, 0)

		# Initialize widgets but don't decide which to show
		self.placeholder_widget = PlaceholderWidget(self)
		self.outliner_widget = None  # Will be created on demand by the controller

		# Connect the signal from the placeholder to our own signal
		self.placeholder_widget.file_validated.connect(self.file_validated)

		# By default, show the placeholder
		self.display_placeholder()

	def _clear_layout(self):
		"""Private method to clear the panel before switching widgets."""
		# Properly remove the previous widget from the layout
		while self.layout.count():
			item = self.layout.takeAt(0)
			widget = item.widget()
			if widget:
				widget.setParent(None)

	# We don't delete the widget itself (deleteLater), as we might want
	# to show it again (e.g., placeholder_widget).
	# The outliner is deleted separately when the window is closed.

	# --- Public methods for managing state (called by Controller) ---

	def display_placeholder(self):
		"""Shows the placeholder widget. Called by the Controller."""
		self._clear_layout()
		if self.outliner_widget:
			# If the outliner exists, it needs to be hidden and possibly deleted
			self.outliner_widget.setParent(None)
		# The controller will need to take care of calling delete_panel() for cleanup
		self.layout.addWidget(self.placeholder_widget)
		self.placeholder_widget.reset_state()

	def display_outliner(self, file_path):
		"""
		Creates and shows the outliner widget. Called by the Controller.
		The controller passes the file_path needed for initialization.
		"""
		self._clear_layout()

		# MVC: The controller decided to load a reference and show the outliner.
		# First, the model logic is executed (loading the reference).
		# It's assumed that the controller already called maya_utils.load_reference(file_path).

		# Now update the view.
		try:
			# Create a new outliner instance
			self.outliner_widget = OutlinerPanel(self, reference_file=file_path)
			self.layout.addWidget(self.outliner_widget)
			return self.outliner_widget  # Return the created widget for further use
		except Exception as e:
			print(f"Failed to create OutlinerPanel: {e}")
			# If something went wrong, return to the placeholder with an error
			self.display_placeholder()
			self.placeholder_widget.set_error_state("Failed to\ndisplay outliner.")
		return None

	def get_active_outliner(self):
		"""Allows the controller to access the active outliner for cleanup."""
		return self.outliner_widget
