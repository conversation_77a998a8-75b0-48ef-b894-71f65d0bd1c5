@echo off
setlocal enabledelayedexpansion

rem Create an empty qrc file
echo ^<RCC^> > icons.qrc
echo 	^<qresource prefix="/"^> >> icons.qrc

rem Loop over all png files in current directory
for %%i in (*.png *.jpg *.jpeg *.bmp *.gif *.svg) do (
  rem Get filename with extension
  set "name=%%~nxi"

  rem Write filename and URL into qrc file with proper syntax
  echo 		^<file^>!name!^</file^> >> icons.qrc
)

rem Close qresource and RCC tags
echo 	^</qresource^> >> icons.qrc
echo ^</RCC^> >> icons.qrc

rem Convert qrc to py 
"D:\Programs\Autodesk\Maya2022\bin3\pyside2-rcc.exe" icons.qrc -o rc_icons.py
endlocal