import os

from ... import *
from ...resources.icons import rc_icons


class BaseToolbar(QToolBar):
	def __init__(self, name, parent=None):
		super().__init__(name, parent)
		self.setIconSize(QSize(18, 18))
		self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonIconOnly)


class ReferenceToolbar(BaseToolbar):
	"""
	Класс для создания кастомного тулбара с логикой включения/выключения кнопок.
	"""
	# Сигнал, который будет испускаться при изменении режима
	override_mode_changed = Signal(str)

	def __init__(self, parent=None):
		super().__init__("OverrideTools", parent)

		self.init_ui()
		self.connect_signals()

	def init_ui(self):
		"""
		Инициализация UI элементов тулбара.
		"""

		# --- Кнопка 1: Главный переключатель ---
		self.override_enabled = QAction(QIcon(":/override_main.svg"), "Override", self)
		self.override_enabled.setCheckable(True)
		self.override_enabled.setToolTip("Toggle override options")

		# --- Группа для радио-кнопок ---
		self.radio_button_group = QActionGroup(self)
		self.radio_button_group.setExclusive(True)

		# --- Радио-кнопки ---
		self.override_group = QAction(QIcon(":/override_group.svg"), "Override Group", self)
		self.override_group.setCheckable(True)
		self.override_group.setToolTip("Process groups and their children")

		self.override_mesh = QAction(QIcon(":/override_item.svg"), "Override Mesh", self)
		self.override_mesh.setCheckable(True)
		self.override_mesh.setToolTip("Process only mesh objects")

		# Добавляем радио-кнопки в группу
		self.radio_button_group.addAction(self.override_group)
		self.radio_button_group.addAction(self.override_mesh)

		# Добавляем все действия на тулбар
		self.addAction(self.override_enabled)
		self.addSeparator()
		self.addActions(self.radio_button_group.actions())

		# Изначально деактивируем группу радио-кнопок
		self.radio_button_group.setEnabled(False)

	def connect_signals(self):
		"""
		Подключение сигналов к слотам.
		"""
		self.override_enabled.toggled.connect(self.on_main_toggle)
		self.radio_button_group.triggered.connect(self._on_override_mode_changed)

	def on_main_toggle(self, checked):
		"""
		Обрабатывает состояние главного переключателя.
		Включает или выключает группу зависимых радио-кнопок.
		"""
		self.radio_button_group.setEnabled(checked)
		if not checked:
			checked_action = self.radio_button_group.checkedAction()
			if checked_action:
				checked_action.setChecked(False)
			# Когда главный переключатель выключен, режим сбрасывается на None
			self.override_mode_changed.emit(None)

	def _on_override_mode_changed(self, action):
		"""
		Слот, который вызывается при выборе нового действия в группе.
		Испускает сигнал с новым режимом.
		"""
		mode = self.get_override_mode()
		self.override_mode_changed.emit(mode)

	def get_override_mode(self):
		"""
		Возвращает текущий выбранный режим переопределения.

		Returns:
			str or None: "group", "mesh", или None, если ничего не выбрано.
		"""
		checked_action = self.radio_button_group.checkedAction()
		if checked_action == self.override_group:
			return "group"
		elif checked_action == self.override_mesh:
			return "mesh"
		return None


class SceneToolbar(BaseToolbar):
	"""
	Класс для создания кастомного тулбара с логикой включения/выключения кнопок.
	"""
	validate_mode_changed = Signal(str)

	def __init__(self, parent=None):
		super().__init__("SceneTools", parent)

		self.init_ui()
		self.connect_signals()

	def init_ui(self):
		"""
		Инициализация UI элементов тулбара.
		"""
		self.validate = QAction(QIcon(":/vld_menu.svg"), "Override", self)
		self.validate.setCheckable(True)
		self.validate.setToolTip("Show validation in color")

		self.addAction(self.validate)

	def connect_signals(self):
		"""
		Подключение сигналов к слотам.
		"""
		self.validate.toggled.connect(self._on_validate_toggle)

	def _on_validate_toggle(self, checked):
		"""
		Слот, который вызывается при выборе нового действия в группе.
		Испускает сигнал с новым режимом.
		"""
		self.validate_mode_changed.emit(checked)
		print(f"SceneToolbar: Validate mode changed to: {checked}")
