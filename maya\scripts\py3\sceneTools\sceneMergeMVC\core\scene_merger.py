# -*- coding: utf-8 -*-

import os
import json
import re
import maya.cmds as cmds
import traceback

from . import maya_utils as Utils
from collections import defaultdict
from enum import Enum

# Цвета для аутлайнера (R, G, B)
COLOR_VALID_EXISTS = (0.4, 0.6, 1.0)  # Синий
COLOR_VALID_NEW = (0.5, 1.0, 0.5)  # Светло-зеленый
COLOR_INVALID = (1.0, 0.6, 0.2)  # Оранжевый


class ObjectType(Enum):
	VALID_EXISTING = "valid_existing"
	VALID_NEW = "valid_new"
	INVALID_EXISTING = "invalid_existing"
	INVALID_NEW = "invalid_new"


class HierarchyPosition(Enum):
	ROOT = "root"
	INTERMEDIATE = "intermediate"
	LEAF = "leaf"


class ProcessingStrategy(Enum):
	REPLACE = "replace"  # Заменить существующий
	CREATE = "create"  # Создать новый
	CREATE_WITH_HIERARCHY = "create_with_hierarchy"  # Создать с восстановлением иерархии
	SKIP = "skip"


class SceneMerger:
	"""
	Класс для анализа, сравнения и слияния объектов референса с основной сценой.
	"""

	def __init__(self, namespace, outliner_panel):
		self.namespace = namespace
		self.outliner_panel = outliner_panel

		self.override_mode = None

		self.valid_patterns = Utils.load_structure_patterns()

		# Списки для хранения результатов анализа
		self.valid_items = []
		self.invalid_items = []
		self.selected_items = []

	def set_override_mode(self, mode):
		"""
		Устанавливает режим override для фильтрации объектов.

		Args:
			mode (OverrideMode): Новый режим override
		"""
		print(f"SceneMerger: Установлен режим override: {mode}")
		self.override_mode = mode

	# def _load_structure_patterns(self):
	# 	"""Загружает и компилирует регулярные выражения из JSON."""
	# 	patterns = []
	# 	# Определяем путь к файлу относительно текущего скрипта
	# 	current_dir = os.path.dirname(__file__)
	# 	config_path = os.path.join(current_dir, '..', 'config', 'structure.json')
	#
	# 	if not os.path.exists(config_path):
	# 		cmds.warning(f"Файл конфигурации не найден: {config_path}")
	# 		return patterns
	#
	# 	with open(config_path, 'r') as f:
	# 		data = json.load(f)
	# 		for p_str in data.get("patterns", []):
	# 			patterns.append(re.compile(p_str))
	# 	return patterns

	def _is_valid_path(self, object_path):
		"""Проверяет, соответствует ли путь объекта одному из шаблонов."""
		# Убираем неймспейс и начальный '|' для сравнения
		clean_path = object_path.replace(f"{self.namespace}:", "")
		print(f"Проверка пути: {clean_path}")
		for pattern in self.valid_patterns:
			if pattern.match(clean_path):
				return True
		return False

	def analyze_and_color(self):
		"""
		Главный метод анализа. Проходит по референсу, определяет валидность
		и окрашивает объекты в аутлайнере.
		"""
		print("Анализ и окрашивание референса...")

		all_ref_transforms = Utils.get_outliner_items(self.outliner_panel)

		for ref_obj in all_ref_transforms:
			is_valid = self._is_valid_path(ref_obj)

			if is_valid:
				self.valid_items.append(ref_obj)
				# Проверяем, существует ли аналог в основной сцене
				# Исправлено: Проверяем полный путь, а не только короткое имя
				scene_obj_name = ref_obj.replace(f"{self.namespace}:", "")
				if cmds.objExists(scene_obj_name):
					self._set_outliner_color(ref_obj, COLOR_VALID_EXISTS)
				else:
					self._set_outliner_color(ref_obj, COLOR_VALID_NEW)
			else:
				self.invalid_items.append(ref_obj)
				self._set_outliner_color(ref_obj, COLOR_INVALID)
		print("Анализ завершен.")

	# def _get_outliner_items(self):
	# 	"""Получает список объектов в аутлайнере."""
	# 	filters = cmds.outlinerEditor(self.outliner_panel, query=True, filter=True)
	# 	print(f"Применяем фильтры: {filters}")
	# 	object_list = cmds.lsThroughFilter(filters)
	# 	print(f"Полученные объекты: {object_list}")
	# 	result_list = cmds.ls(object_list, type="transform", long=True) or []
	# 	return result_list

	def _set_outliner_color(self, obj, color):
		"""Применяет цвет к объекту в аутлайнере."""
		try:
			cmds.setAttr(f"{obj}.useOutlinerColor", 1)
			cmds.setAttr(f"{obj}.outlinerColor", *color)
		except Exception as e:
			# Может возникнуть ошибка, если объект заблокирован
			print(f"Не удалось применить цвет к {obj}: {e}")

	def _reset_outliner_color(self, obj_name):
		"""Сбрасывает цвет объекта в аутлайнере к значению по умолчанию."""
		try:
			if cmds.objExists(obj_name) and cmds.attributeQuery('useOutlinerColor', node=obj_name, exists=True):
				cmds.setAttr(f"{obj_name}.useOutlinerColor", 0)
		except Exception as e:
			print(f"Не удалось сбросить цвет для {obj_name}: {e}")

	# --- Helper Methods for Merging ---

	def _get_scene_counterpart(self, ref_object_path):
		"""Возвращает имя объекта в основной сцене по его референс-пути."""
		return ref_object_path.replace(f"{self.namespace}:", "")

	def _rename_to_remove_namespace(self, object_path):
		"""
		Переименовывает объект, удаляя из его имени неймспейс.
		Возвращает новое имя объекта.
		"""
		short_name = object_path.split('|')[-1]
		new_name = self._get_scene_counterpart(short_name)
		try:
			return cmds.rename(object_path, new_name)
		except Exception as e:
			print(f"Не удалось переименовать {object_path} в {new_name}: {e}")
			return None

	def _find_existing_parent_in_main_scene(self, ref_object_path):
		"""
		Находит ближайшего существующего родителя в основной сцене для референс-объекта.
		Возвращает кортеж: (путь_к_существующему_родителю, [список_недостающих_потомков]).
		Если родитель не найден (объект в корне), возвращает (None, [список_пути]).
		"""
		path_parts = ref_object_path.split('|')

		# Начинаем проверку с родителя объекта
		for i in range(len(path_parts) - 2, 0, -1):
			potential_parent_ref_path = '|'.join(path_parts[:i + 1])
			potential_parent_scene_path = self._get_scene_counterpart(potential_parent_ref_path)

			if cmds.objExists(potential_parent_scene_path):
				missing_children = [self._get_scene_counterpart(p) for p in path_parts[i + 1:]]
				return (potential_parent_scene_path, missing_children)

		# Если ни один родитель не найден, значит, вся иерархия до корня новая
		full_path_no_ns = [self._get_scene_counterpart(p) for p in path_parts[1:]]  # Убираем пустой элемент в начале
		return (None, full_path_no_ns)

	def _perform_full_merge_(self, object_list):
		"""
		Основная логика для Merge All и Merge Valid.
		Дублирует, перемещает и переименовывает объекты из списка.
		"""
		if not object_list:
			cmds.warning("Список объектов для слияния пуст.")
			return False

		# Фильтруем список, оставляя только те объекты, которых нет в основной сцене
		new_objects_to_create = [
			obj for obj in object_list
			if not cmds.objExists(self._get_scene_counterpart(obj))
		]

		print(f"Объекты для создания: {new_objects_to_create}")

		if not new_objects_to_create:
			cmds.warning("Все объекты из списка уже существуют в сцене. Слияние не требуется.")
			return True  # Считаем операцию успешной, так как делать нечего

		try:
			for ref_obj_path in new_objects_to_create:
				# Пропускаем, если объект уже был создан как дочерний другого
				if not cmds.objExists(ref_obj_path):
					print(f"Объект {ref_obj_path} не существует в референсе. Пропускаем...")
					continue

				# 1. Дублируем объект. Дублируем только сам узел, без иерархии ниже.
				duplicated_obj_list = cmds.duplicate(ref_obj_path, parentOnly=True, un=True)
				if not duplicated_obj_list:
					cmds.warning(f"Не удалось дублировать объект: {ref_obj_path}")
					continue
				duplicated_obj = duplicated_obj_list[0]
				print(f"Дублированный объект: {duplicated_obj}")

				# 2. Находим правильного родителя в основной сцене
				parent_ref = cmds.listRelatives(ref_obj_path, parent=True, fullPath=True)[0]
				if parent_ref:
					parent_in_scene = self._get_scene_counterpart(parent_ref)
					print(f"Родитель в сцене: {parent_in_scene}")
					if cmds.objExists(parent_in_scene):
						try:
							duplicated_obj = cmds.parent(duplicated_obj, parent_in_scene)
						except Exception as e:
							traceback.print_exc()
							print(f"Не удалось переместить {duplicated_obj} под родителя {parent_in_scene}: {e}")

				# 3. Переименовываем, удаляя неймспейс (после перемещения)
				self._rename_to_remove_namespace(parent_ref)

			print("Слияние успешно завершено.")
			return True

		except Exception as e:
			cmds.warning(f"Ошибка во время слияния: {e}")
			traceback.print_exc()
			return False

	def merge_all(self):
		"""Сливает все объекты из референса (валидные и невалидные)."""
		all_items = self.valid_items + self.invalid_items
		return self._perform_full_merge(all_items)

	def merge_valid(self):
		"""Сливает только валидные объекты."""
		print(f"Количество валидных объектов для слияния: {len(self.valid_items)}")

		filtered_valid_items = self._filter_objects_by_override_mode(self.valid_items)
		# список валидных и отфильтрованных по оверрайду объектов
		print(f"Valid Override items: {filtered_valid_items}")

		return self._perform_full_merge(filtered_valid_items)

	def _reset_hierarchy_outliner_color(self, root_object):
		"""Recursively resets the outliner color for the entire hierarchy starting from root_object."""
		# Reset color for all descendants
		children = cmds.listRelatives(root_object, allDescendents=True, fullPath=True) or []
		for child in children:
			self._reset_outliner_color(child)

		# Reset color for the root object itself
		self._reset_outliner_color(root_object)

	def _rename_hierarchy_to_remove_namespace(self, root_object):
		"""Рекурсивно удаляет неймспейс у всей иерархии, начиная с root_object."""
		# Сначала переименовываем сам корневой объект
		new_root_name = self._rename_to_remove_namespace(root_object)

		# Получаем всех его потомков
		children = cmds.listRelatives(new_root_name, allDescendents=True, fullPath=True) or []

		# Переименовываем потомков, начиная с самых глубоких
		# Это предотвращает ошибки, когда родитель переименовывается раньше потомка
		for child in reversed(children):
			new_child_name = self._rename_to_remove_namespace(child)

		return new_root_name

	def _get_valid_ref_selection(self):
		"""Получает и валидирует выбранные в аутлайнере объекты референса."""
		selected_in_outliner = cmds.ls(sl=True, l=True) or []
		if not selected_in_outliner:
			cmds.warning("Ничего не выбрано в аутлайнере референсов.")
			return []

		ref_selection = [obj for obj in selected_in_outliner if obj.startswith(f"|{self.namespace}:")]
		if not ref_selection:
			cmds.warning("Выбранные объекты не принадлежат загруженному референсу.")
			return []

		return ref_selection

	def _merge_single_object(self, ref_obj_path):
		"""Сливает один объект из референса в основную сцену."""
		scene_obj_path = self._get_scene_counterpart(ref_obj_path)

		if cmds.objExists(scene_obj_path):
			self._replace_existing_object(ref_obj_path, scene_obj_path)
		else:
			self._create_new_object(ref_obj_path, scene_obj_path)

	def _replace_existing_object(self, ref_obj_path, scene_obj_path):
		"""Улучшенная замена существующего объекта с проверками."""
		print(f"Заменяем существующий объект: {scene_obj_path}")

		try:
			# Проверяем, что объекты существуют
			if not cmds.objExists(ref_obj_path):
				print(f"Предупреждение: референсный объект {ref_obj_path} не существует")
				return False

			if not cmds.objExists(scene_obj_path):
				print(f"Предупреждение: объект для замены {scene_obj_path} не существует")
				return False

			# Сохраняем информацию об иерархии
			scene_parent = cmds.listRelatives(scene_obj_path, parent=True, fullPath=True)
			scene_children = cmds.listRelatives(scene_obj_path, children=True, fullPath=True) or []

			# Проверяем блокировки
			if cmds.lockNode(scene_obj_path, query=True, lock=True)[0]:
				cmds.lockNode(scene_obj_path, lock=False)
				print(f"Разблокирован объект: {scene_obj_path}")

			# Дублируем с полной иерархией
			duplicated_group = cmds.duplicate(ref_obj_path, renameChildren=True, un=True)[0]

			# Сохраняем дочерние объекты, если они есть в сцене, но не в референсе
			orphaned_children = self._find_orphaned_children(scene_obj_path, ref_obj_path)

			# Удаляем старый объект
			cmds.delete(scene_obj_path)

			# Размещаем новый объект
			final_object = self._place_object_in_hierarchy(duplicated_group, scene_parent)

			# Возвращаем потерянных детей
			self._restore_orphaned_children(final_object, orphaned_children)

			# Переименовываем для удаления namespace
			final_object = self._rename_hierarchy_to_remove_namespace(final_object)

			print(f"Объект успешно заменен: {final_object}")
			return True

		except Exception as e:
			print(f"Ошибка при замене объекта {scene_obj_path}: {e}")
			return False

	def _find_orphaned_children(self, scene_obj_path, ref_obj_path):
		"""Находит детей, которые есть в сцене, но отсутствуют в референсе."""
		scene_children = cmds.listRelatives(scene_obj_path, children=True, fullPath=True) or []
		ref_children = cmds.listRelatives(ref_obj_path, children=True, fullPath=True) or []

		# Конвертируем имена референсных детей в имена сцены
		ref_children_scene_names = [self._get_scene_counterpart(child) for child in ref_children]

		orphaned = []
		for scene_child in scene_children:
			if scene_child not in ref_children_scene_names:
				orphaned.append(scene_child)

		return orphaned

	def _restore_orphaned_children(self, new_parent, orphaned_children):
		"""Возвращает потерянных детей новому родителю."""
		for orphaned_child in orphaned_children:
			if cmds.objExists(orphaned_child):
				try:
					cmds.parent(orphaned_child, new_parent)
					print(f"Восстановлен потерянный ребенок: {orphaned_child}")
				except Exception as e:
					print(f"Не удалось восстановить ребенка {orphaned_child}: {e}")

	def _create_new_object(self, ref_obj_path, scene_obj_path):
		"""Улучшенное создание нового объекта с проверками."""
		print(f"Создаем новый объект: {scene_obj_path}")

		try:
			# Проверяем, что референсный объект существует
			if not cmds.objExists(ref_obj_path):
				print(f"Предупреждение: референсный объект {ref_obj_path} не существует")
				return False

			# Проверяем, что объект действительно не существует в сцене
			if cmds.objExists(scene_obj_path):
				print(f"Предупреждение: объект {scene_obj_path} уже существует, используем замену")
				return self._replace_existing_object(ref_obj_path, scene_obj_path)

			# Находим правильное место в иерархии
			parent_in_scene, missing_hierarchy = self._find_existing_parent_in_main_scene(ref_obj_path)

			# Дублируем объект
			duplicated_group = cmds.duplicate(ref_obj_path, renameChildren=True, un=True)[0]

			# Создаем недостающую иерархию (если не создана ранее)
			target_parent = self._ensure_parent_hierarchy_exists(parent_in_scene, missing_hierarchy[:-1])

			# Размещаем объект
			final_object = self._place_object_in_hierarchy(duplicated_group, [target_parent] if target_parent else None)

			# Переименовываем для удаления namespace
			final_object = self._rename_hierarchy_to_remove_namespace(final_object)

			print(f"Объект успешно создан: {final_object}")
			return True

		except Exception as e:
			print(f"Ошибка при создании объекта {scene_obj_path}: {e}")
			return False

	def _ensure_parent_hierarchy_exists(self, parent_in_scene, missing_hierarchy):
		"""Обеспечивает существование родительской иерархии."""
		current_parent = parent_in_scene

		for group_name in missing_hierarchy:
			# Убираем namespace из имени группы
			clean_group_name = group_name.split('|')[-1].replace(f"{self.namespace}:", "")

			# Проверяем, не создана ли группа уже
			full_path = f"{current_parent}|{clean_group_name}" if current_parent else clean_group_name

			if not cmds.objExists(full_path):
				new_group = cmds.group(empty=True, name=clean_group_name)
				if current_parent:
					new_group = cmds.parent(new_group, current_parent)[0]
				current_parent = new_group
			else:
				current_parent = full_path

		return current_parent

	def _place_object_in_hierarchy(self, obj, parent_list):
		"""Размещает объект в правильном месте иерархии."""
		if parent_list and parent_list[0]:
			return cmds.parent(obj, parent_list[0])[0]
		else:
			return cmds.parent(obj, world=True)

	def _create_missing_hierarchy(self, parent_in_scene, missing_hierarchy):
		"""Создает недостающие группы в иерархии."""
		current_parent = parent_in_scene

		for group_name in missing_hierarchy:
			new_group = cmds.group(empty=True, name=group_name)
			if current_parent:
				new_group = cmds.parent(new_group, current_parent)[0]
			current_parent = new_group

		return current_parent

	def merge_selected(self):
		"""Комплексная обработка выбранных объектов с учетом всех сценариев."""
		ref_selection = self._get_valid_ref_selection()
		if not ref_selection:
			return False

		print(f"Выбранные объекты до фильтрации: {len(ref_selection)}")

		# Применяем фильтрацию по режиму override
		filtered_selection = self._filter_objects_by_override_mode(ref_selection)

		print(f"Выбранные объекты после фильтрации: {len(filtered_selection)}")

		if not filtered_selection:
			cmds.warning("После применения фильтра override не осталось объектов для обработки.")
			return False

		try:
			# 1. Анализируем выделение
			analysis = self._analyze_selection(ref_selection)

			# 2. Планируем обработку
			processing_plan = self._create_processing_plan(analysis)

			# 3. Выполняем план
			self._execute_processing_plan(processing_plan)

			print("Слияние выбранных объектов успешно завершено.")
			return True

		except Exception as e:
			cmds.warning(f"Ошибка во время слияния выбранных объектов: {e}")
			import traceback
			traceback.print_exc()
			return False

	def _perform_full_merge(self, object_list):
		"""
		Основная логика для Merge All и Merge Valid.
		Использует уже реализованную логику _merge_single_object для консистентности.
		"""
		if not object_list:
			cmds.warning("Список объектов для слияния пуст.")
			return False

		# Фильтруем список, оставляя только те объекты, которых нет в основной сцене
		new_objects_to_create = self._filter_new_objects(object_list)

		if not new_objects_to_create:
			cmds.warning("Все объекты из списка уже существуют в сцене. Слияние не требуется.")
			return True

		try:
			for ref_obj_path in new_objects_to_create:
				if not cmds.objExists(ref_obj_path):
					print(f"Объект {ref_obj_path} не существует в референсе. Пропускаем...")
					continue

				# Используем уже реализованную логику создания нового объекта
				scene_obj_path = self._get_scene_counterpart(ref_obj_path)
				self._create_new_object(ref_obj_path, scene_obj_path)

			print("Слияние успешно завершено.")
			return True

		except Exception as e:
			cmds.warning(f"Ошибка во время слияния: {e}")
			import traceback
			traceback.print_exc()
			return False

	def _filter_new_objects(self, object_list):
		"""Фильтрует список объектов, оставляя только те, которых нет в основной сцене."""
		new_objects = [
			obj for obj in object_list
			if not cmds.objExists(self._get_scene_counterpart(obj))
		]
		print(f"Объекты для создания: {new_objects}")
		return new_objects

	def _filter_objects_by_override_mode(self, object_list):
		"""
		Фильтрует список объектов в соответствии с текущим режимом override.

		Args:
			object_list (list): Исходный список объектов

		Returns:
			list: Отфильтрованный список объектов
		"""
		if self.override_mode is None:
			return object_list

		filtered_objects = []

		if self.override_mode == "mesh":
			# Оставляем только mesh трансформы
			for obj in object_list:
				if Utils.is_mesh_transform(obj):
					filtered_objects.append(obj)
					print(f"SceneMerger: Mesh объект включен: {obj}")
				else:
					print(f"SceneMerger: Объект пропущен (не mesh): {obj}")

		elif self.override_mode == "group":
			# Для групп включаем группу и всех ее потомков
			processed = set()  # Избегаем дублирования

			for obj in object_list:
				if obj in processed:
					continue

				if Utils.is_group_transform(obj):
					# Это группа - добавляем ее и всех потомков
					filtered_objects.append(obj)
					processed.add(obj)
					print(f"SceneMerger: Группа включена: {obj}")

					# Добавляем всех потомков группы
					descendants = Utils.get_all_group_descendants(obj)
					for descendant in descendants:
						if descendant not in processed and descendant in object_list:
							filtered_objects.append(descendant)
							processed.add(descendant)
							print(f"SceneMerger: Потомок группы включен: {descendant}")
				else:
					# Не группа - проверяем, не является ли потомком уже обработанной группы
					is_descendant_of_processed_group = False
					for processed_obj in processed:
						if obj.startswith(processed_obj + "|"):
							is_descendant_of_processed_group = True
							break

					if not is_descendant_of_processed_group:
						# Объект не является потомком уже обработанной группы
						# Проверяем, есть ли среди исходного списка его родительская группа
						parent_group = Utils.find_parent_group_in_list(obj, object_list)
						if not parent_group:
							# Родительской группы в списке нет, добавляем объект
							filtered_objects.append(obj)
							processed.add(obj)
							print(f"SceneMerger: Одиночный объект включен: {obj}")

		print(f"SceneMerger: Отфильтровано {len(filtered_objects)} из {len(object_list)} объектов")
		return filtered_objects

	# Альтернативный вариант с более детальным контролем:
	def _perform_full_merge_detailed(self, object_list):
		"""
		Альтернативная реализация с более детальным контролем процесса.
		Разделяет объекты на существующие и новые для разной обработки.
		"""
		if not object_list:
			cmds.warning("Список объектов для слияния пуст.")
			return False

		existing_objects, new_objects = self._categorize_objects(object_list)

		if not new_objects and not existing_objects:
			cmds.warning("Нет объектов для обработки.")
			return True

		try:
			# Обрабатываем существующие объекты (замена)
			for ref_obj_path in existing_objects:
				if cmds.objExists(ref_obj_path):
					scene_obj_path = self._get_scene_counterpart(ref_obj_path)
					self._replace_existing_object(ref_obj_path, scene_obj_path)

			# Обрабатываем новые объекты (создание)
			for ref_obj_path in new_objects:
				if cmds.objExists(ref_obj_path):
					scene_obj_path = self._get_scene_counterpart(ref_obj_path)
					self._create_new_object(ref_obj_path, scene_obj_path)

			print("Слияние успешно завершено.")
			return True

		except Exception as e:
			cmds.warning(f"Ошибка во время слияния: {e}")
			import traceback
			traceback.print_exc()
			return False

	def _categorize_objects(self, object_list):
		"""Разделяет объекты на существующие в сцене и новые."""
		existing_objects = []
		new_objects = []

		for obj in object_list:
			scene_obj_path = self._get_scene_counterpart(obj)
			if cmds.objExists(scene_obj_path):
				existing_objects.append(obj)
			else:
				new_objects.append(obj)

		print(f"Существующие объекты для замены: {existing_objects}")
		print(f"Новые объекты для создания: {new_objects}")

		return existing_objects, new_objects

	def _analyze_selection(self, selection):
		"""Полный анализ выделенных объектов."""
		analysis = {
			'objects': {},  # {obj_path: ObjectInfo}
			'hierarchy_groups': [],  # Связанные группы объектов
			'processing_order': [],  # Порядок обработки
			'conflicts': [],  # Обнаруженные конфликты
			'missing_parents': {}  # {child: [missing_parents]}
		}

		# Анализируем каждый объект
		for obj_path in selection:
			obj_info = self._analyze_single_object(obj_path)
			analysis['objects'][obj_path] = obj_info

		# Анализируем иерархические связи
		analysis['hierarchy_groups'] = self._find_hierarchy_groups(selection)

		# Находим недостающих родителей
		analysis['missing_parents'] = self._find_missing_parents(selection)

		# Определяем порядок обработки (от корня к листьям)
		analysis['processing_order'] = self._determine_processing_order(selection)

		# Обнаруживаем конфликты
		analysis['conflicts'] = self._detect_conflicts(analysis)

		return analysis

	def _analyze_single_object(self, obj_path):
		"""Анализирует один объект и определяет его характеристики."""
		scene_path = self._get_scene_counterpart(obj_path)

		return {
			'ref_path': obj_path,
			'scene_path': scene_path,
			'is_valid': self._is_valid_path(obj_path),
			'exists_in_scene': cmds.objExists(scene_path),
			'hierarchy_position': self._get_hierarchy_position(obj_path),
			'object_type': self._classify_object_type(obj_path, scene_path),
			'parent_ref': cmds.listRelatives(obj_path, parent=True, fullPath=True),
			'children_ref': cmds.listRelatives(obj_path, children=True, fullPath=True) or [],
			'depth_level': len(obj_path.split('|')) - 1
		}

	def _classify_object_type(self, obj_path, scene_path):
		"""Классифицирует объект по типу."""
		is_valid = self._is_valid_path(obj_path)
		exists = cmds.objExists(scene_path)

		if is_valid and exists:
			return ObjectType.VALID_EXISTING
		elif is_valid and not exists:
			return ObjectType.VALID_NEW
		elif not is_valid and exists:
			return ObjectType.INVALID_EXISTING
		else:
			return ObjectType.INVALID_NEW

	def _get_hierarchy_position(self, obj_path):
		"""Определяет позицию объекта в иерархии."""
		parent = cmds.listRelatives(obj_path, parent=True, fullPath=True)
		children = cmds.listRelatives(obj_path, children=True, fullPath=True)

		if not parent:
			return HierarchyPosition.ROOT
		elif not children:
			return HierarchyPosition.LEAF
		else:
			return HierarchyPosition.INTERMEDIATE

	def _find_hierarchy_groups(self, selection):
		"""Находит связанные группы объектов в иерархии."""
		groups = []
		processed = set()

		for obj_path in selection:
			if obj_path in processed:
				continue

			# Находим всех связанных объектов (предки и потомки)
			group = self._find_connected_objects(obj_path, selection)
			if len(group) > 1:
				groups.append(group)
				processed.update(group)

		return groups

	def _find_connected_objects(self, root_obj, selection):
		"""Находит всех связанных объектов в выделении."""
		connected = {root_obj}

		# Ищем предков
		parent = cmds.listRelatives(root_obj, parent=True, fullPath=True)
		while parent and parent[0] in selection:
			connected.add(parent[0])
			parent = cmds.listRelatives(parent[0], parent=True, fullPath=True)

		# Ищем потомков
		def find_descendants(obj):
			children = cmds.listRelatives(obj, children=True, fullPath=True) or []
			for child in children:
				if child in selection:
					connected.add(child)
					find_descendants(child)

		find_descendants(root_obj)
		return list(connected)

	def _find_missing_parents(self, selection):
		"""Находит недостающих родителей для создания полной иерархии."""
		missing_parents = {}

		for obj_path in selection:
			scene_path = self._get_scene_counterpart(obj_path)
			if not cmds.objExists(scene_path):
				parent_in_scene, missing_hierarchy = self._find_existing_parent_in_main_scene(obj_path)
				if missing_hierarchy and len(missing_hierarchy) > 1:  # Исключаем сам объект
					missing_parents[obj_path] = missing_hierarchy[:-1]

		return missing_parents

	def _determine_processing_order(self, selection):
		"""Определяет порядок обработки объектов (от корня к листьям)."""
		# Сортируем по глубине иерархии
		depth_sorted = sorted(selection, key=lambda x: len(x.split('|')))
		return depth_sorted

	def _detect_conflicts(self, analysis):
		"""Обнаруживает потенциальные конфликты в обработке."""
		conflicts = []

		# Проверяем конфликты типа "родитель и ребенок выделены одновременно"
		for obj_path, obj_info in analysis['objects'].items():
			parent = obj_info['parent_ref']
			if parent and parent[0] in analysis['objects']:
				conflicts.append({
					'type': 'parent_child_conflict',
					'parent': parent[0],
					'child': obj_path,
					'resolution': 'process_parent_first'
				})

		return conflicts

	def _create_processing_plan(self, analysis):
		"""Создает план обработки на основе анализа."""
		plan = {
			'pre_create_hierarchy': [],  # Создать недостающую иерархию
			'replace_existing': [],  # Заменить существующие объекты
			'create_new': [],  # Создать новые объекты
			'post_cleanup': []  # Финальная очистка
		}

		# Группируем объекты по стратегии обработки
		for obj_path in analysis['processing_order']:
			obj_info = analysis['objects'][obj_path]
			strategy = self._determine_processing_strategy(obj_info, analysis)

			if strategy == ProcessingStrategy.REPLACE:
				plan['replace_existing'].append(obj_path)
			elif strategy == ProcessingStrategy.CREATE_WITH_HIERARCHY:
				if obj_path in analysis['missing_parents']:
					plan['pre_create_hierarchy'].append(obj_path)
				plan['create_new'].append(obj_path)
			elif strategy == ProcessingStrategy.CREATE:
				plan['create_new'].append(obj_path)
		# SKIP обрабатывается автоматически

		return plan

	def _determine_processing_strategy(self, obj_info, analysis):
		"""Определяет стратегию обработки для объекта."""
		obj_type = obj_info['object_type']

		# Правила по умолчанию
		strategy_rules = {
			ObjectType.VALID_EXISTING: ProcessingStrategy.REPLACE,
			ObjectType.VALID_NEW: ProcessingStrategy.CREATE_WITH_HIERARCHY,
			ObjectType.INVALID_EXISTING: ProcessingStrategy.REPLACE,  # Обрабатываем невалидные
			ObjectType.INVALID_NEW: ProcessingStrategy.CREATE_WITH_HIERARCHY  # Обрабатываем невалидные
		}

		return strategy_rules.get(obj_type, ProcessingStrategy.SKIP)

	def _execute_processing_plan(self, plan):
		"""Выполняет план обработки поэтапно."""

		# Этап 1: Создаем недостающую иерархию
		for obj_path in plan['pre_create_hierarchy']:
			self._ensure_hierarchy_exists(obj_path)

		# Этап 2: Заменяем существующие объекты
		for obj_path in plan['replace_existing']:
			scene_path = self._get_scene_counterpart(obj_path)
			self._replace_existing_object(obj_path, scene_path)

		# Этап 3: Создаем новые объекты
		for obj_path in plan['create_new']:
			scene_path = self._get_scene_counterpart(obj_path)
			if not cmds.objExists(scene_path):  # Проверяем, что не создан на предыдущих этапах
				self._create_new_object(obj_path, scene_path)

		# Этап 4: Финальная очистка
		self._perform_post_cleanup(plan)

	def _ensure_hierarchy_exists(self, obj_path):
		"""Обеспечивает существование всей иерархии для объекта."""
		parent_in_scene, missing_hierarchy = self._find_existing_parent_in_main_scene(obj_path)

		if missing_hierarchy and len(missing_hierarchy) > 1:
			# Создаем недостающих родителей (исключая сам объект)
			current_parent = parent_in_scene
			for group_name in missing_hierarchy[:-1]:
				if not cmds.objExists(group_name):
					new_group = cmds.group(empty=True, name=group_name.split('|')[-1].replace(f"{self.namespace}:", ""))
					if current_parent:
						new_group = cmds.parent(new_group, current_parent)[0]
					current_parent = new_group
				else:
					current_parent = group_name

	def _perform_post_cleanup(self, plan):
		"""Выполняет финальную очистку после обработки."""
		# Сбрасываем цвета для всех обработанных объектов
		all_processed = (plan['replace_existing'] + plan['create_new'])

		for obj_path in all_processed:
			scene_path = self._get_scene_counterpart(obj_path)
			if cmds.objExists(scene_path):
				self._reset_hierarchy_outliner_color(scene_path)

	def _log_processing_summary(self, plan):
		"""Выводит сводку обработки."""
		print("=== Сводка обработки ===")
		print(f"Создано иерархий: {len(plan['pre_create_hierarchy'])}")
		print(f"Заменено объектов: {len(plan['replace_existing'])}")
		print(f"Создано новых объектов: {len(plan['create_new'])}")
		print("========================")

	def _validate_object_for_processing(self, obj_path):
		"""Проверяет, можно ли обработать объект."""
		if not cmds.objExists(obj_path):
			return False, "Объект не существует"

		if cmds.lockNode(obj_path, query=True, lock=True)[0]:
			return False, "Объект заблокирован"

		if cmds.referenceQuery(obj_path, isNodeReferenced=True):
			ref_file = cmds.referenceQuery(obj_path, filename=True)
			if not cmds.file(ref_file, query=True, exists=True):
				return False, "Файл референса не найден"

		return True, "OK"

	def _get_processing_statistics(self, analysis):
		"""Возвращает статистику для обработки."""
		stats = {
			'total_objects': len(analysis['objects']),
			'valid_existing': 0,
			'valid_new': 0,
			'invalid_existing': 0,
			'invalid_new': 0,
			'hierarchy_groups': len(analysis['hierarchy_groups']),
			'missing_parents_count': len(analysis['missing_parents']),
			'conflicts_count': len(analysis['conflicts'])
		}

		for obj_info in analysis['objects'].values():
			obj_type = obj_info['object_type']
			if obj_type == ObjectType.VALID_EXISTING:
				stats['valid_existing'] += 1
			elif obj_type == ObjectType.VALID_NEW:
				stats['valid_new'] += 1
			elif obj_type == ObjectType.INVALID_EXISTING:
				stats['invalid_existing'] += 1
			elif obj_type == ObjectType.INVALID_NEW:
				stats['invalid_new'] += 1

		return stats

	def _print_processing_plan(self, plan, stats):
		"""Выводит детальный план обработки."""
		print("\n=== ПЛАН ОБРАБОТКИ ===")
		print(f"Общий анализ:")
		print(f"  - Всего объектов: {stats['total_objects']}")
		print(f"  - Валидных существующих: {stats['valid_existing']}")
		print(f"  - Валидных новых: {stats['valid_new']}")
		print(f"  - Невалидных существующих: {stats['invalid_existing']}")
		print(f"  - Невалидных новых: {stats['invalid_new']}")
		print(f"  - Групп иерархии: {stats['hierarchy_groups']}")
		print(f"  - Конфликтов: {stats['conflicts_count']}")

		print(f"\nПлан выполнения:")
		print(f"  1. Создание иерархий: {len(plan['pre_create_hierarchy'])} объектов")
		print(f"  2. Замена существующих: {len(plan['replace_existing'])} объектов")
		print(f"  3. Создание новых: {len(plan['create_new'])} объектов")
		print("=====================\n")
