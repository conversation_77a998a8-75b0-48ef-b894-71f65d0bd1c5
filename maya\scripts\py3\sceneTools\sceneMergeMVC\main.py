import maya.cmds as cmds
import sys
import traceback

import sceneTools.sceneMergeMVC.ui.main_window as MainWindow


def reload_all_modules(module_name):
	for m in list(sys.modules):
		if module_name in m:
			del (sys.modules[m])


def main():
	"""
	Показывает главное окно MergeScenesWindow.
	"""
	reload_all_modules("sceneMergeMVC")

	if cmds.window("MergeScenesMainWindow", exists=True):
		cmds.deleteUI("MergeScenesMainWindow")
	try:
		main_window = MainWindow.MergeScenesWindow()
		main_window.show()
	except RuntimeError as e:
		traceback.print_exc()
	# win = MergeScenesWindow()
	# win.show()
	print("Showing MergeScenesWindow")


if __name__ == "__main__":
	main()
