# -*- coding: utf-8 -*-
import maya.cmds as cmds
import os
import json
import re

COLOR_VALID_NEW = (0.5, 1.0, 0.5)  # Светло-зеленый
COLOR_INVALID = (1.0, 0.6, 0.2)  # Оранжевый


def get_current_scene_path():
	"""Возвращает полный путь к текущей открытой сцене."""
	return cmds.file(query=True, sceneName=True)


def create_custom_outliner():
	"""
	Создает отдельную панель Outliner и настраивает ее для нашего инструмента.

	- Создает временное окно, чтобы в нем разместить панель.
	- Создает саму панель Outliner.
	- Настраивает фильтры редактора Outliner:
	  - Показывает только DAG объекты (геометрию, группы и т.д.).
	  - Скрывает ноды референсов по умолчанию.
	  - Скрывает шейпы, чтобы не загромождать вид.
	- Удаляет временное окно, оставляя только панель (она становится UI-элементом без родителя).
	- Возвращает уникальное имя созданной панели для дальнейшего использования.

	Returns:
		str: Имя созданной панели Outliner.
	"""
	# Создаем временное окно, так как outlinerPanel требует родительского лэйаута
	temp_window = cmds.window()
	# Внутри окна создаем лэйаут, куда поместим аутлайнер
	form = cmds.formLayout()

	# Создаем панель аутлайнера. Она автоматически получает уникальное имя.
	# Мы задаем ей родителя (form) и делаем ее единственным элементом в лэйауте.
	panel = cmds.outlinerPanel()
	cmds.formLayout(form, edit=True,
	                attachForm=[(panel, 'top', 0), (panel, 'left', 0), (panel, 'bottom', 0), (panel, 'right', 0)])

	# Получаем имя самого редактора аутлайнера внутри панели
	outliner_editor = cmds.outlinerPanel(panel, query=True, outlinerEditor=True)
	print(f"Создан Outliner Editor: {outliner_editor}")

	# Применяем необходимые фильтры к редактору
	cmds.outlinerEditor(outliner_editor,
	                    edit=True,
	                    showShapes=False,  # Скрываем шейпы (e.g., pSphereShape1)
	                    showDagOnly=True,  # Показываем только DAG-иерархию
	                    showReferenceNodes=False,  # Не показывать узел референса
	                    showReferenceMembers=True)  # Но показывать его содержимое

	# Удаляем временное окно, сама панель при этом остается в памяти Maya
	cmds.deleteUI(temp_window, window=True)

	print(f"Создана и настроена панель аутлайнера: {panel}")
	return panel


def outliner_panel():
	cmds.setParent('MainSplitterMergeScenes')
	cmds.paneLayout()
	panel = cmds.outlinerPanel()
	outliner = cmds.outlinerPanel(panel, query=True, outlinerEditor=True)
	cmds.outlinerEditor(outliner, edit=True, mainListConnection='worldList', selectionConnection='modelList',
	                    showShapes=False, showReferenceNodes=False, showReferenceMembers=False,
	                    showAttributes=False, showConnected=False, showAnimCurvesOnly=False, autoExpand=False,
	                    showDagOnly=True, ignoreDagHierarchy=False, expandConnections=False, showNamespace=True,
	                    showCompounds=True, showNumericAttrsOnly=False, highlightActive=True,
	                    autoSelectNewObjects=False, doNotSelectNewObjects=False, transmitFilters=False,
	                    showSetMembers=False, setFilter='defaultSetFilter', ignoreHiddenAttribute=False,
	                    ignoreOutlinerColor=False)
	return panel, outliner


def load_reference(file_path):
	"""
	Загружает референсный файл в сцену Maya.

	"""
	delete_reference()
	cmds.file(file_path, reference=True, namespace='r')
	filters = cmds.itemFilter(bn=('*:*'), neg=1)
	cmds.outlinerEditor('outlinerPanel1', e=1, showReferenceNodes=False, showReferenceMembers=False,
	                    filter=filters)
	visible_reference_objects()


# def delete_reference():
# 	"""
# 	Удаляет все референсные ноды из сцены Maya.
#
# 	"""
# 	references = cmds.ls(type='reference')
# 	print(f"Удаление референсов: {references}")
# 	if references:
# 		for ref in references:
# 			cmds.file(ref, removeReference=True)

def delete_reference():
	for ref in cmds.ls(references=True):
		try:
			cmds.file(unloadReference=ref)
		except:
			pass
	for ref in cmds.ls(references=True):
		try:
			cmds.file(rr=1, f=1, referenceNode=ref)
		except:
			pass


def visible_reference_objects():
	meshes = cmds.ls('*:*', o=1, s=1)
	for m in meshes:
		cmds.setAttr(m + '.visibility', 0)


def perform_merge(object_list):
	"""
	Основная логика импорта референсов.
	Импортирует указанные объекты. Maya автоматически обрабатывает
	иерархию и предотвращает дублирование существующих групп.
	"""
	if not object_list:
		print("Список объектов для слияния пуст.")
		return False

	print(f"Выполняется слияние для {len(object_list)} объектов...")


# try:
# 	# Импортируем только указанные ноды из референса
# 	cmds.file(importReference=True, referenceNode=self.ref_node, nodes=object_list)
# 	print("Слияние успешно завершено.")
# 	return True
# except Exception as e:
# 	cmds.warning(f"Ошибка во время слияния: {e}")
# 	return False


def reset_outliner_colors():
	"""Сбрасывает цвета для всех объектов в сцене."""
	print("Сброс цветов в аутлайнере...")
	all_transforms = cmds.ls(type="transform") or []
	for obj in all_transforms:
		if cmds.attributeQuery("useOutlinerColor", node=obj, exists=True):
			try:
				cmds.setAttr(f"{obj}.useOutlinerColor", 0)
			except:
				pass  # Атрибут может быть заблокирован


def get_all_outliner_panels():
	"""Получает все существующие outliner панели"""
	return cmds.getPanel(type='outlinerPanel')


def get_outliner_editor_name(panel_name):
	"""Получает имя outliner editor по имени панели"""
	try:
		return cmds.outlinerPanel(panel_name, query=True, outlinerEditor=True)
	except:
		return None


def get_current_filter(outliner_editor):
	"""Получает текущий фильтр outliner"""
	try:
		return cmds.outlinerEditor(outliner_editor, query=True, filter=True)
	except:
		return None


def get_filter_types(filter_name):
	"""Получает типы объектов, разрешенные фильтром"""
	if not filter_name:
		return []
	try:
		return cmds.itemFilter(filter_name, query=True, byType=True)
	except:
		return []


def get_filtered_objects_by_filter(filter_name):
	"""Получает объекты, соответствующие фильтру"""
	if not filter_name:
		return cmds.ls(dag=True, long=True)

	try:
		# Получаем типы из фильтра
		filter_types = get_filter_types(filter_name)
		if filter_types:
			objects = []
			for obj_type in filter_types:
				if obj_type == 'mesh':
					# Для mesh получаем transform родителей
					shapes = cmds.ls(type='mesh', long=True)
					for shape in shapes:
						parents = cmds.listRelatives(shape, parent=True, fullPath=True)
						if parents:
							objects.extend(parents)
				else:
					objects.extend(cmds.ls(type=obj_type, long=True))
			return list(set(objects))
		else:
			return cmds.ls(dag=True, long=True)
	except:
		return []


def analyze_outliner_state(outliner_editor):
	"""Анализирует состояние outliner и возвращает детальную информацию"""
	info = {
		'editor_name': outliner_editor,
		'filter': None,
		'filter_types': [],
		'show_shapes': False,
		'show_dag_only': False,
		'show_reference_nodes': False,
		'show_reference_members': False,
		'objects_count': 0,
		'visible_objects': []
	}

	try:
		# Получаем настройки outliner
		info['filter'] = cmds.outlinerEditor(outliner_editor, query=True, filter=True)
		info['show_shapes'] = cmds.outlinerEditor(outliner_editor, query=True, showShapes=True)
		info['show_dag_only'] = cmds.outlinerEditor(outliner_editor, query=True, showDagOnly=True)
		info['show_reference_nodes'] = cmds.outlinerEditor(outliner_editor, query=True, showReferenceNodes=True)
		info['show_reference_members'] = cmds.outlinerEditor(outliner_editor, query=True, showReferenceMembers=True)

		# Получаем типы фильтра
		if info['filter']:
			info['filter_types'] = get_filter_types(info['filter'])

		# Получаем видимые объекты
		info['visible_objects'] = get_filtered_objects_by_filter(info['filter'])
		info['objects_count'] = len(info['visible_objects'])

	except Exception as e:
		print(f"Ошибка при анализе outliner: {e}")

	return info


def print_outliner_analysis(outliner_editor):
	"""Выводит детальный анализ состояния outliner"""
	info = analyze_outliner_state(outliner_editor)

	print(f"\n=== Анализ Outliner: {info['editor_name']} ===")
	print(f"Фильтр: {info['filter']}")
	print(f"Типы фильтра: {info['filter_types']}")
	print(f"Показывать shapes: {info['show_shapes']}")
	print(f"Только DAG: {info['show_dag_only']}")
	print(f"Reference nodes: {info['show_reference_nodes']}")
	print(f"Reference members: {info['show_reference_members']}")
	print(f"Количество объектов: {info['objects_count']}")

	if info['objects_count'] <= 10:
		print("Видимые объекты:")
		for obj in info['visible_objects']:
			print(f"  - {obj}")
	else:
		print("Первые 10 видимых объектов:")
		for obj in info['visible_objects'][:10]:
			print(f"  - {obj}")
		print(f"  ... и еще {info['objects_count'] - 10}")


def compare_outliner_contents(editor1, editor2):
	"""Сравнивает содержимое двух outliner'ов"""
	info1 = analyze_outliner_state(editor1)
	info2 = analyze_outliner_state(editor2)

	objects1 = set(info1['visible_objects'])
	objects2 = set(info2['visible_objects'])

	print(f"\n=== Сравнение Outliner'ов ===")
	print(f"{editor1}: {len(objects1)} объектов")
	print(f"{editor2}: {len(objects2)} объектов")

	common = objects1.intersection(objects2)
	only_in_1 = objects1.difference(objects2)
	only_in_2 = objects2.difference(objects1)

	print(f"Общие объекты: {len(common)}")
	print(f"Только в {editor1}: {len(only_in_1)}")
	print(f"Только в {editor2}: {len(only_in_2)}")

	return {
		'common': list(common),
		'only_in_first': list(only_in_1),
		'only_in_second': list(only_in_2)
	}


def is_mesh_transform(transform_node):
	"""
	Проверяет, является ли трансформ непосредственным родителем mesh shape.

	Args:
		transform_node (str): Полный путь к трансформу

	Returns:
		bool: True если трансформ имеет дочерний mesh shape
	"""
	try:
		# Получаем все дочерние shape узлы
		shapes = cmds.listRelatives(transform_node, shapes=True, f=True, noIntermediate=True) or []

		# Проверяем, есть ли среди них mesh
		for shape in shapes:
			if cmds.nodeType(shape) == "mesh":
				return True

		return False
	except:
		return False


def is_group_transform(transform_node):
	"""
	Проверяет, является ли трансформ группой (имеет дочерние трансформы, но не имеет shapes).

	Args:
		transform_node (str): Полный путь к трансформу

	Returns:
		bool: True если это группа
	"""
	try:
		# Проверяем наличие дочерних трансформов
		children = cmds.listRelatives(transform_node, children=True, type="transform") or []
		# Проверяем отсутствие собственных shapes (кроме intermediate)
		shapes = cmds.listRelatives(transform_node, shapes=True, noIntermediate=True) or []

		return len(children) > 0 and len(shapes) == 0
	except:
		return False


def get_all_group_descendants(group_transform):
	"""
	Получает всех потомков группы рекурсивно.

	Args:
		group_transform (str): Полный путь к группе

	Returns:
		list: Список всех потомков
	"""
	descendants = []
	try:
		all_descendants = cmds.listRelatives(group_transform, allDescendents=True, type="transform",
		                                     fullPath=True) or []
		descendants.extend(all_descendants)
	except:
		pass

	return descendants


def find_parent_group_in_list(obj, object_list):
	"""
	Ищет родительскую группу объекта в списке объектов.

	Args:
		obj (str): Объект для поиска родителя
		object_list (list): Список для поиска

	Returns:
		str or None: Родительская группа или None
	"""
	obj_parts = obj.split("|")

	# Идем от родителя к корню
	for i in range(len(obj_parts) - 2, 0, -1):
		potential_parent = "|".join(obj_parts[:i + 1])
		if potential_parent in object_list and self._is_group_transform(potential_parent):
			return potential_parent

	return None


def analyze_and_color(outliner_panel_name):
	"""
	Главный метод анализа. Проходит по объектам оутлайнера, определяет валидность
	и окрашивает объекты в аутлайнере.
	"""
	print("Анализ и окрашивание ...")

	all_transforms = get_outliner_items(outliner_panel_name)
	print(f"Найдено {len(all_transforms)} трансформов в аутлайнере.")
	valid_patterns = load_structure_patterns()

	for obj in all_transforms:
		is_valid = is_valid_pattern(obj, valid_patterns)
		print(f"Проверка объекта: {obj} - {'валидный' if is_valid else 'невалидный'}")

		if is_valid:
			# valid_items.append(obj)
			# Проверяем, существует ли аналог в основной сцене
			# Исправлено: Проверяем полный путь, а не только короткое имя
			# scene_obj_name = obj.replace(f"r:", "")
			# if cmds.objExists(scene_obj_name):
			# 	set_outliner_color(obj, COLOR_VALID_EXISTS)
			# else:
			set_outliner_color(obj, COLOR_VALID_NEW)
		else:
			# invalid_items.append(obj)
			set_outliner_color(obj, COLOR_INVALID)


def is_valid_pattern(object_path, valid_patterns):
	"""Проверяет, соответствует ли путь объекта одному из шаблонов."""
	for pattern in valid_patterns:
		if pattern.match(object_path):
			return True
	return False


def load_structure_patterns():
	"""Загружает и компилирует регулярные выражения из JSON."""
	patterns = []
	# Определяем путь к файлу относительно текущего скрипта
	current_dir = os.path.dirname(__file__)
	config_path = os.path.join(current_dir, '..', 'config', 'structure.json')

	if not os.path.exists(config_path):
		cmds.warning(f"Файл конфигурации не найден: {config_path}")
		return patterns

	with open(config_path, 'r') as f:
		data = json.load(f)
		for p_str in data.get("patterns", []):
			patterns.append(re.compile(p_str))
	return patterns


def get_outliner_items_(outliner_panel_name):
	filters = cmds.outlinerEditor(outliner_panel_name, query=True, filter=True)
	object_list = cmds.lsThroughFilter(filters)
	if not object_list:
		print("Объекты через фильтр не найдены")
		return []

	parent_list = cmds.listRelatives(object_list, parent=True, typ="transform", fullPath=True) or []
	result_set = set(parent_list)

	for current_obj in parent_list:
		current = current_obj

		# Поднимаемся по иерархии до корня
		while current:
			parent = cmds.listRelatives(current, parent=True, typ="transform", fullPath=True)
			if not parent:
				break
			current = parent[0]
			result_set.add(current)

	# Конвертируем в список и сортируем
	result_list = sorted(list(result_set))
	return result_list


def get_outliner_items(outliner_panel_name):
	"""
	Более оптимизированная версия с кэшированием и улучшенной логикой.
	"""
	# Получаем фильтры и объекты
	filters = cmds.outlinerEditor(outliner_panel_name, query=True, filter=True)
	object_list = cmds.lsThroughFilter(filters)

	if not object_list:
		return []

	# Получаем родительские объекты
	parent_list = cmds.listRelatives(object_list, parent=True, typ="transform", fullPath=True) or []

	# Используем set для хранения всех найденных объектов
	all_objects = set(parent_list)

	# Кэш для избежания повторных вызовов cmds.listRelatives
	parent_cache = {}

	def get_all_parents(obj_path):
		"""Рекурсивно получает всех родителей объекта с кэшированием."""
		if obj_path in parent_cache:
			return parent_cache[obj_path]

		parents = []
		current = obj_path

		while current:
			parent = cmds.listRelatives(current, parent=True, typ="transform", fullPath=True)
			if not parent:
				break
			current = parent[0]
			parents.append(current)

		parent_cache[obj_path] = parents
		return parents

	# Собираем всех родителей для каждого объекта
	for obj in parent_list:
		all_parents = get_all_parents(obj)
		all_objects.update(all_parents)

	return sorted(list(all_objects))


def set_outliner_color(obj, color):
	"""Применяет цвет к объекту в аутлайнере."""
	try:
		cmds.setAttr(f"{obj}.useOutlinerColor", 1)
		cmds.setAttr(f"{obj}.outlinerColor", *color)
	except Exception as e:
		# Может возникнуть ошибка, если объект заблокирован
		print(f"Не удалось применить цвет к {obj}: {e}")


def reset_outliner_color(obj_name):
	"""Сбрасывает цвет объекта в аутлайнере к значению по умолчанию."""
	try:
		if cmds.objExists(obj_name) and cmds.attributeQuery('useOutlinerColor', node=obj_name, exists=True):
			cmds.setAttr(f"{obj_name}.useOutlinerColor", 0)
	except Exception as e:
		print(f"Не удалось сбросить цвет для {obj_name}: {e}")
