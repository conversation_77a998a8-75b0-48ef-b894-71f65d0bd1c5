{"description": "Hierarchy patterns for tank models. Regular expressions are used.", "patterns": ["^\\|lod[0-9]\\|phys_collision\\|tank_.*_phys$", "^\\|lod[0-9]$", "^\\|lod[0-9]\\|hull$", "^\\|lod[0-9]\\|hull\\|hull_[0-9][0-9]$", "^\\|lod[0-9]\\|hull\\|hull_[0-9][0-9]\\|hull_[0-9][0-9]+.*$", "^\\|lod[0-9]\\|hull\\|hull_[0-9][0-9]\\|upgrades$", "^\\|lod[0-9]\\|hull\\|hull_[0-9][0-9]\\|upgrades\\|.*$", "^\\|lod[0-9]\\|turret$", "^\\|lod[0-9]\\|turret\\|turret_[0-9][0-9]$", "^\\|lod[0-9]\\|turret\\|turret_[0-9][0-9]\\|turret_[0-9][0-9]+.*$", "^\\|lod[0-9]\\|turret\\|turret_[0-9][0-9]\\|upgrades$", "^\\|lod[0-9]\\|turret\\|turret_[0-9][0-9]\\|upgrades\\|.*$", "^\\|lod[0-9]\\|gun$", "^\\|lod[0-9]\\|gun\\|gun_[0-9][0-9]$", "^\\|lod[0-9]\\|gun\\|gun_[0-9][0-9]\\|gun_[0-9][0-9].*+$", "^\\|lod[0-9]\\|gun\\|gun_[0-9][0-9]\\|upgrades$", "^\\|lod[0-9]\\|gun\\|gun_[0-9][0-9]\\|upgrades\\|.*$", "^\\|lod[0-9]\\|chassis$", "^\\|lod[0-9]\\|chassis\\|chassis_[0-9][0-9]$", "^\\|lod[0-9]\\|chassis\\|chassis_[0-9][0-9]\\|tracks_[0-9][0-9]$", "^\\|lod[0-9]\\|chassis\\|chassis_[0-9][0-9]\\|tracks_[0-9][0-9]\\|(left|right)$", "^\\|lod[0-9]\\|chassis\\|chassis_[0-9][0-9]\\|tracks_[0-9][0-9]\\|(left|right)\\|track_(l|r)_(normal|crash)$", "^\\|lod[0-9]\\|chassis\\|chassis_[0-9][0-9]\\|chassis_[0-9][0-9]+.*$"]}