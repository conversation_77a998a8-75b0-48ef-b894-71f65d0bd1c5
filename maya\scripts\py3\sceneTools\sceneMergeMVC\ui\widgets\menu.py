# -*- coding: utf-8 -*-

from ... import *


class Menu(QMenuBar):
	"""
	Класс для создания главного меню приложения.
	"""

	def __init__(self, parent=None):
		super(Menu, self).__init__(parent)
		self.init_ui()

	def init_ui(self):
		"""
		Инициализация UI элементов меню.
		"""
		pass

	def set_outliner_panel(self, outliner_panel):
		"""
		Устанавливает связь с outliner панелью для MVC взаимодействия.
		"""
		self.outliner_panel = outliner_panel


# Создаем основные пункты меню
# self.file_menu = self.addMenu("File")
# self.options_menu = self.addMenu("Options")
# self.help_menu = self.addMenu("Help")


# Здесь можно будет добавлять действия (actions) в эти меню
# Например:
# exit_action = QAction("Exit", self)
# self.file_menu.addAction(exit_action)

class MainMenu(Menu):
	def __init__(self, parent=None):
		super().__init__(parent)

	def init_ui(self):
		self.help_menu = self.addMenu("Help")


class BaseOutlinerMenu(QMenuBar):
	"""
	Базовый класс для меню outliner панелей.
	Содержит общую функциональность для всех типов outliner меню.
	"""

	# Сигналы для MVC взаимодействия
	action_triggered = Signal(str, object)  # (action_name, data)

	def __init__(self, parent=None):
		super().__init__(parent)
		self.outliner_panel = None  # Ссылка на связанную панель outliner
		self.init_base_ui()

	def init_base_ui(self):
		"""
		Инициализация базового UI.
		Создает общие действия, доступные для всех типов outliner.
		"""
		# Общие действия
		self.refresh_action = QAction("Refresh", self)
		self.refresh_action.setToolTip("Refresh outliner content")
		self.refresh_action.triggered.connect(lambda: self.action_triggered.emit("refresh", None))

		self.expand_all_action = QAction("Expand All", self)
		self.expand_all_action.triggered.connect(lambda: self.action_triggered.emit("expand_all", None))

		self.collapse_all_action = QAction("Collapse All", self)
		self.collapse_all_action.triggered.connect(lambda: self.action_triggered.emit("collapse_all", None))

		# Добавляем общие действия
		self.addAction(self.refresh_action)
		self.addAction(self.expand_all_action)
		self.addAction(self.collapse_all_action)
		self.addSeparator()

	def set_outliner_panel(self, outliner_panel):
		"""
		Устанавливает связь с outliner панелью для MVC взаимодействия.
		"""
		self.outliner_panel = outliner_panel


class ReferenceMenu(Menu):
	"""
	Контекстное меню для панели с референсами.
	Содержит специфичные для работы с референсами действия.
	"""

	def __init__(self, parent=None):
		super().__init__(parent)
		self.init_scene_ui()

	def init_scene_ui(self):
		"""
		Инициализация специфичных для сцены действий.
		"""
		self.how_menu = self.addMenu("Show")


class SceneMenu(Menu):
	"""
	Контекстное меню для панели с текущей сценой.
	Содержит специфичные для работы со сценой действия.
	"""

	def __init__(self, parent=None):
		super().__init__(parent)
		self.init_scene_ui()

	def init_scene_ui(self):
		"""
		Инициализация специфичных для сцены действий.
		"""
		self.how_menu = self.addMenu("Show")

# Действия для работы со сценой
# self.validate_scene_action = QAction("Validate Scene", self)
# self.validate_scene_action.setToolTip("Run scene validation checks")
# self.validate_scene_action.triggered.connect(
# 	lambda: self.action_triggered.emit("validate_scene", None)
# )

# self.export_selected_action = QAction("Export Selected...", self)
# self.export_selected_action.setToolTip("Export selected objects to file")
# self.export_selected_action.triggered.connect(
# 	lambda: self.action_triggered.emit("export_selected", None)
# )
#
# self.cleanup_scene_action = QAction("Cleanup Scene", self)
# self.cleanup_scene_action.setToolTip("Remove unused nodes and optimize scene")
# self.cleanup_scene_action.triggered.connect(
# 	lambda: self.action_triggered.emit("cleanup_scene", None)
# )

# Добавляем действия в меню
# self.addAction(self.validate_scene_action)
# self.addAction(self.export_selected_action)
# self.addSeparator()
# self.addAction(self.cleanup_scene_action)
