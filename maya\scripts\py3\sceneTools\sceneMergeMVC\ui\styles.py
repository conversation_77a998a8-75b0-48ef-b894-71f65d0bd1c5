def load_main_style() -> str:
	"""
	Returns the QSS style for the main application window.
	"""
	style = """
        QDialog#MergeScenesMainWindow { border: 1px solid gray; }
        QSplitter{border: 1px solid gray; padding: 3px}
        QToolBar {
		                spacing: 0px;
		                padding: 5px;
		                margin: 0px;
		                border: none;
		            }
		            QToolBar::separator {
		                width: 1px;
		                margin: 0px;
		            }
		            QToolButton {
		                width: {icon_size.width()}px;
                        height: {icon_size.height()}px;
		                padding: 0px;
		                margin: 0px;
		                border: none;
		                background: transparent;
		            }
		            QToolButton:hover {
		                background: rgba(255, 255, 255, 0.1);
		            }
		            QToolButton:pressed {
		                background: rgba(255, 255, 255, 0.2);
		            }
		            QToolButton:checked {
		                background: rgba(82, 133, 166, 1.0);
		            }
    """
	return style


def get_placeholder_widget_style() -> str:
	"""
	Returns the default QSS style for the placeholder widget.
	"""
	return """
        QLabel#placeholder-widget {
            border: 2px dashed #aaa; 
            border-radius: 5px;
            background-color: #f0f0f0; 
            color: #666; 
            font-size: 14px;
        }
        QLabel#placeholder-widget:hover {
            border-color: #0078d4; 
            background-color: #e6f3ff;
        }
    """


def get_placeholder_widget_error_style() -> str:
	"""
	Returns the QSS error style for the placeholder widget.
	"""
	return """
        QLabel#placeholder-widget {
            border: 2px solid #dc3545; 
            background-color: #f8d7da; 
            color: #721c24; 
            border-radius: 5px; 
            font-size: 14px;
        }
    """


def get_placeholder_widget_drag_active_style() -> str:
	"""
	Returns the QSS style for the placeholder widget during drag operation.
	"""
	return """
        QLabel#placeholder-widget {
            border: 2px solid #0078d4; 
            background-color: #cce7ff;
        }
    """
