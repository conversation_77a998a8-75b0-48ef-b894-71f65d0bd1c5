# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 5.15.2
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x02\x9a\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 viewBox=\x22\
20 10 160 180\x22>\x0d\
\x0a    <path fill=\
\x22#FE6D00\x22\x0d\x0a     \
     d=\x22M20 10h7\
0c3 1 27 26 30 3\
0v30h30c3 1 27 2\
6 30 30v90H80v-6\
0H20V10Zm120 100\
V85H95v90h70v-65\
h-25Zm10-17v7h7l\
-7-7ZM90 40h7l-7\
-7v7Zm15 30V50H8\
0V25H35v90h45V70\
h25Z\x22/>\x0d\x0a    <pa\
th fill=\x22#B6B4B4\
\x22\x0d\x0a          d=\x22\
m121 134 4 1 3 2\
c4 1 8-1 12-3l3 \
2c5 3 8 8 9 14 1\
 2 1 5-1 7l-9 5c\
-8 1-18 1-25-1-3\
-1-6-3-7-5-2-3 0\
-8 1-10 2-5 6-9 \
10-12Zm9-24a11 1\
1 0 1 1 2 23 11 \
11 0 0 1-2-23Z\x22/\
>\x0d\x0a    <path fil\
l=\x22#FE6D00\x22\x0d\x0a   \
       d=\x22M50 35\
h15v45H50V35Zm0 \
55h15v15H50V90Zm\
100-80v15a28 28 \
0 0 1 26 14c6 11\
 3 16 4 26h-10c0\
-7 0-14-3-20l-3-\
3c-4-5-9-6-14-6v\
15c-2-2-20-19-20\
-21l7-8 13-12Z\x22/\
>\x0d\x0a</svg>\
\x00\x00\x01\xc1\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 viewBox=\x22\
20 10 160 180\x22>\x0d\
\x0a    <path fill=\
\x22#FE6D00\x22\x0d\x0a     \
     d=\x22M20 10h7\
0c3 1 27 26 30 3\
0v30h30l30 30v90\
H80v-60H20V10Zm1\
45 164v-64h-25V8\
5H95v90h70v-1ZM3\
5 115h45V70h25V5\
0H80V25H35v90Zm1\
15-15h7l-7-7v7ZM\
97 40l-7-7v7h7Z\x22\
/>\x0d\x0a    <path fi\
ll=\x22#FE6D00\x22\x0d\x0a  \
        d=\x22M50 3\
5h15v45H50V35Zm0\
 55h15v15H50V90Z\
m100-80v15a28 28\
 0 0 1 26 14c6 1\
1 3 16 4 26h-10c\
0-7 0-14-3-20l-3\
-3c-4-5-9-6-14-6\
v15l-20-21 7-8 1\
3-12Z\x22/>\x0d\x0a</svg>\
\
\x00\x00\x03\x9e\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 viewBox=\x22\
20 10 160 180\x22>\x0d\
\x0a    <path fill=\
\x22#FE6D00\x22\x0d\x0a     \
     d=\x22M20 10h7\
0c3 1 27 26 30 3\
0v30h30l30 30v90\
H80v-60H20V10Zm7\
5 165h70v-65h-25\
V85H95v90Zm-60-6\
0h45V70h25V50H80\
V25H35v90Zm55-82\
v7h7l-7-7Zm60 67\
h7l-7-7v7Z\x22/>\x0d\x0a \
   <path fill=\x22#\
B6B4B5\x22\x0d\x0a       \
   d=\x22m123 136 5\
 2c3 1 6 0 10-2 \
4 2 7 6 9 10 1 3\
 2 6 1 9-3 3-6 4\
-9 5h-18c-3-1-7-\
2-8-5-2-2-1-4-1-\
6 2-6 5-10 11-13\
Zm6-19a9 9 0 1 1\
 4 18 9 9 0 0 1-\
4-18Z\x22/>\x0d\x0a    <p\
ath fill=\x22#B6B4B\
5\x22\x0d\x0a          d=\
\x22M108 133c5 1 6 \
2 11 1-8 7-9 10-\
10 20l-5-1c-2 0-\
4-2-5-4v-6c2-4 5\
-8 9-10Zm44 1h1c\
4 3 7 7 8 11v5c-\
2 3-6 4-9 5l-1-6\
-3-8-5-6 3 1 6-2\
Zm-38-18c3 0 5 0\
 7 2-2 4-2 6-2 1\
0v4l-4 1-3-1c-2-\
2-4-3-4-6v-6c1-2\
 4-3 6-4Zm30 1h3\
c3 0 4 1 6 3l1 7\
-5 5h-7v-2c1-4 0\
-8-2-12l4-1Z\x22/>\x0d\
\x0a    <path fill=\
\x22#FE6D00\x22\x0d\x0a     \
     d=\x22M50 35h1\
5v45H50V35Zm0 55\
h15v15H50V90Zm10\
0-80v15a28 28 0 \
0 1 26 14c6 11 3\
 16 4 26h-10c0-7\
 0-14-3-20l-3-3c\
-4-5-9-6-14-6v15\
l-20-21 7-8 13-1\
2Z\x22/>\x0d\x0a</svg>\
\x00\x00\x06\xed\
<\
svg xmlns=\x22http:\
//www.w3.org/200\
0/svg\x22 xmlns:xli\
nk=\x22http://www.w\
3.org/1999/xlink\
\x22 version=\x221.1\x22 \
width=\x22256\x22\x0d\x0a   \
  height=\x22256\x22 v\
iewBox=\x220 0 256 \
256\x22 xml:space=\x22\
preserve\x22>\x0d\x0a\x0d\x0a<d\
efs>\x0d\x0a</defs>\x0d\x0a \
   <g style=\x22str\
oke: none; strok\
e-width: 0; stro\
ke-dasharray: no\
ne; stroke-linec\
ap: butt; stroke\
-linejoin: miter\
; stroke-miterli\
mit: 10; fill: n\
one; fill-rule: \
nonzero; opacity\
: 1;\x22\x0d\x0a       tr\
ansform=\x22transla\
te(1.40659340659\
34016 1.40659340\
65934016) scale(\
2.81 2.81)\x22>\x0d\x0a\x09<\
path d=\x22M 60.879\
 31.299 c -6.173\
 5.123 -11.7 10.\
614 -16.569 16.3\
7 h -0.021 h -0.\
001 c -2.531 -4.\
833 -5.459 -9.59\
1 -8.752 -14.248\
 l -14.077 4.832\
 c 6.182 6.566 1\
1.571 13.473 16.\
011 20.67 l 6.24\
3 10.118 l 4.665\
 -9.973 c 4.161 \
-8.895 9.903 -17\
.532 17.283 -25.\
655 c 7.361 -8.1\
3 13.293 -13.679\
 23.373 -20.676 \
C 77.638 18.82 7\
0.1 23.623 60.87\
9 31.299 z\x22\x0d\x0a   \
       style=\x22st\
roke: none; stro\
ke-width: 1; str\
oke-dasharray: n\
one; stroke-line\
cap: butt; strok\
e-linejoin: mite\
r; stroke-miterl\
imit: 10; fill: \
rgb(0,196,51); f\
ill-rule: nonzer\
o; opacity: 1;\x22\x0d\
\x0a          trans\
form=\x22 matrix(1 \
0 0 1 0 0) \x22 str\
oke-linecap=\x22rou\
nd\x22/>\x0d\x0a        <\
path d=\x22M 76.876\
 29.21 c 2.368 4\
.761 3.708 10.12\
1 3.708 15.79 C \
80.584 64.62 64.\
62 80.584 45 80.\
584 S 9.416 64.6\
2 9.416 45 S 25.\
379 9.416 45 9.4\
16 c 9.278 0 17.\
734 3.572 24.075\
 9.409 c 2.586 -\
1.794 5.273 -3.5\
57 8.057 -5.287 \
C 68.958 5.192 5\
7.576 0 45 0 C 2\
0.187 0 0 20.187\
 0 45 s 20.187 4\
5 45 45 s 45 -20\
.188 45 -45 c 0 \
-8.039 -2.129 -1\
5.586 -5.838 -22\
.125 C 81.592 24\
.964 79.173 27.0\
79 76.876 29.21 \
z\x22\x0d\x0a            \
  style=\x22stroke:\
 none; stroke-wi\
dth: 1; stroke-d\
asharray: none; \
stroke-linecap: \
butt; stroke-lin\
ejoin: miter; st\
roke-miterlimit:\
 10; fill: #fa6c\
05; fill-rule: n\
onzero; opacity:\
 1;\x22\x0d\x0a          \
    transform=\x22 \
matrix(1 0 0 1 0\
 0) \x22 stroke-lin\
ecap=\x22round\x22/>\x0d\x0a\
</g>\x0d\x0a</svg>\
"

qt_resource_name = b"\
\x00\x11\
\x0a\x92\x04\xa7\
\x00o\
\x00v\x00e\x00r\x00r\x00i\x00d\x00e\x00_\x00i\x00t\x00e\x00m\x00.\x00s\x00v\x00g\
\
\x00\x11\
\x07\xef\x04\xc7\
\x00o\
\x00v\x00e\x00r\x00r\x00i\x00d\x00e\x00_\x00m\x00a\x00i\x00n\x00.\x00s\x00v\x00g\
\
\x00\x12\
\x04&\xc1\xe7\
\x00o\
\x00v\x00e\x00r\x00r\x00i\x00d\x00e\x00_\x00g\x00r\x00o\x00u\x00p\x00.\x00s\x00v\
\x00g\
\x00\x0c\
\x07\xf5\x16\xa7\
\x00v\
\x00l\x00d\x00_\x00m\x00e\x00n\x00u\x00.\x00s\x00v\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x04\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00P\x00\x00\x00\x00\x00\x01\x00\x00\x04c\
\x00\x00\x01\x98\x83\x91!\x92\
\x00\x00\x00(\x00\x00\x00\x00\x00\x01\x00\x00\x02\x9e\
\x00\x00\x01\x98\x83\x90\xa5!\
\x00\x00\x00z\x00\x00\x00\x00\x00\x01\x00\x00\x08\x05\
\x00\x00\x01\x94i\xd0\xb1\xec\
\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x98\x83\x90\xf4\xb5\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
