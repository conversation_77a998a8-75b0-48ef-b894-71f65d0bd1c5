from ... import *
from maya import cmds
from maya import OpenMayaUI as omui

from ...core import maya_utils
# from ...core import filter_module
from .toolbar import ReferenceToolbar, SceneToolbar
from .menu import ReferenceMenu, SceneMenu


def panel_point(self, panel):
	ptr = omui.MQtUtil.findControl(panel)
	panel_layout = wrapInstance(int(ptr), QWidget)
	return panel_layout


class OutlinerPanel(QWidget):
	"""
	OutlinerPanel следует MVC паттерну:

	MODEL:
	- self.reference_file - данные о файле референса
	- Maya scene data через cmds.outlinerEditor

	VIEW:
	- self.outliner_widget - Maya outliner UI
	- self.toolbar - соответствующий тулбар (ReferenceToolbar/SceneToolbar)
	- self.panel_menu - соответствующее меню (ReferenceMenu/SceneMenu)

	CONTROLLER:
	- self._handle_menu_action() - обработка действий меню
	- self._handle_override_mode_changed() - обработка изменений в тулбаре
	- Методы действий (_refresh_outliner, _expand_all, etc.)
	"""

	override_mode_changed = Signal(str)
	validation_toggled = Signal(bool)

	def __init__(self, parent=None, reference_file=None):
		super().__init__(parent)
		self.panel_name = None
		self.outliner_panel = None
		self.outliner_widget = None
		self.reference_file = reference_file
		self.panel_type = "reference" if reference_file else "scene"

		# UI компоненты
		self.panel_label = None
		self.toolbar = None
		self.panel_menu = None

		self.init_ui()

	def init_ui(self):
		# Главный вертикальный макет
		main_layout = QVBoxLayout(self)
		main_layout.setContentsMargins(0, 0, 0, 0)

		# 1. Создаем и добавляем label вверху
		self._init_panel_label()
		if self.panel_label:
			main_layout.addWidget(self.panel_label)

		# 2. Создаем контейнер для панели с содержимым
		panel_container = QWidget()
		panel_layout = QVBoxLayout(panel_container)
		panel_layout.setContentsMargins(0, 0, 0, 0)

		# 3. Создаем контекстное меню и добавляем как menuBar
		self._init_panel_menu()
		if self.panel_menu:
			panel_layout.setMenuBar(self.panel_menu)

		# 4. Создаем и добавляем соответствующий тулбар
		self._init_toolbar()
		if self.toolbar:
			panel_layout.addWidget(self.toolbar)

		# 5. Создаем Maya outliner
		self._create_maya_outliner()

		# 6. Встраиваем Maya outliner в layout
		if self.outliner_widget:
			panel_layout.addWidget(self.outliner_widget)

		# 7. Добавляем контейнер панели в главный макет
		main_layout.addWidget(panel_container)

	def _create_maya_outliner(self):
		"""
		Создание Maya outliner и его настройка
		"""
		# КЛЮЧЕВОЕ ОТЛИЧИЕ: Создаем постоянный контейнер Maya UI
		# Создаем постоянное окно Maya (не временное!)
		self.maya_window = cmds.window(title="OutlinerContainer", visible=False)

		# Устанавливаем это окно как родителя
		cmds.setParent(self.maya_window)

		# Создаем paneLayout внутри окна (как в работающем примере)
		pane_layout = cmds.paneLayout()

		# Теперь создаем outliner панель внутри правильного контекста
		self.panel_name = cmds.outlinerPanel(parent=pane_layout)

		# Получаем outliner editor
		self.outliner_panel = cmds.outlinerPanel(self.panel_name, query=True, outlinerEditor=True)

		# Создаем фильтры
		self._setup_filters()

		print(f"Создан outliner: {self.panel_name} с редактором: {self.outliner_panel}")

		# Теперь находим Qt виджет и встраиваем его
		ptr = omui.MQtUtil.findControl(self.panel_name)
		if ptr:
			self.outliner_widget = wrapInstance(int(ptr), QWidget)
			print("Outliner успешно создан и готов к встраиванию")
		else:
			print(f"ОШИБКА: Не удалось найти контрол {self.panel_name}")

	def _setup_filters(self):
		"""
		Настройка фильтров для outliner в зависимости от типа панели
		"""
		# Создаем фильтры
		r_filter = cmds.itemFilter(bn=('r:*'))
		transforms_filter = cmds.itemFilter(byType='transform')
		joint_filter = cmds.itemFilter(byType='joint')
		intersect_transform_filter = cmds.itemFilter(intersect=[r_filter, transforms_filter])
		clear_ref_filter = cmds.itemFilter(difference=[intersect_transform_filter, joint_filter])

		dag_mesh_filter = cmds.itemFilter(byType='mesh')
		# dag_transform_filter = cmds.itemFilter(byType=["transform", "mesh"])
		clear_dag_filter = cmds.itemFilter(difference=[dag_mesh_filter, r_filter])
		# clear_dag_filter = cmds.itemFilter(difference=[dag_transform_filter, r_filter])

		# if not cmds.itemFilter('meshTransformFilter', exists=True):
		# 	cmds.itemFilter(
		# 		'meshTransformFilter',
		# 		byScript='has_mesh_descendant')
		#
		# clear_dag_filter = "meshTransformFilter"

		if self.reference_file:
			# СЦЕНАРИЙ 1: ЛЕВАЯ ПАНЕЛЬ (показываем только референс)
			print(f"OutlinerPanel: Настройка для отображения референса: {os.path.basename(self.reference_file)}")

			cmds.outlinerEditor(self.outliner_panel, edit=True, mainListConnection='worldList',
			                    selectionConnection='modelList',
			                    showShapes=False, showReferenceNodes=False, showReferenceMembers=False,
			                    showAttributes=False, showConnected=False, showAnimCurvesOnly=False, autoExpand=False,
			                    showDagOnly=True, ignoreDagHierarchy=False, expandConnections=False, showNamespace=True,
			                    showCompounds=True, showNumericAttrsOnly=False, highlightActive=True,
			                    autoSelectNewObjects=False, doNotSelectNewObjects=False, transmitFilters=False,
			                    showSetMembers=False, ignoreHiddenAttribute=False,
			                    ignoreOutlinerColor=False, filter=clear_ref_filter)
		else:
			# СЦЕНАРИЙ 2: ПРАВАЯ ПАНЕЛЬ (показываем основную сцену, скрываем референсы)
			print("OutlinerPanel: Настройка для отображения основной сцены (без референсов).")

			cmds.outlinerEditor(self.outliner_panel, edit=True,
			                    mainListConnection='worldList',
			                    selectionConnection='modelList',
			                    showShapes=False,
			                    showDagOnly=True,
			                    showReferenceNodes=False,  # Ключевой флаг: скрываем узлы референсов
			                    showReferenceMembers=False,  # Ключевой флаг: скрываем содержимое референсов
			                    showNamespace=True,
			                    filter=clear_dag_filter)  # Применяем DAG фильтр

	def set_filter(self):
		r_filter = cmds.itemFilter(bn=('r:*'))
		transforms_filter = cmds.itemFilter(byType='transform')
		joint_filter = cmds.itemFilter(byType='joint')
		intersect_transform_filter = cmds.itemFilter(intersect=[r_filter, transforms_filter])
		clear_ref_filter = cmds.itemFilter(difference=[intersect_transform_filter, joint_filter])
		dag_mesh_filter = cmds.itemFilter(byType='mesh')
		clear_dag_filter = cmds.itemFilter(difference=[dag_mesh_filter, clear_ref_filter])
		cmds.outlinerEditor(self.outliner_panel, edit=True, filter=clear_dag_filter)

	def delete_panel(self):
		"""Правильно удаляем все созданные компоненты"""
		if self.panel_name and cmds.panel(self.panel_name, exists=True):
			cmds.deleteUI(self.panel_name, panel=True)
			print(f"Панель outliner '{self.panel_name}' удалена")

		if hasattr(self, 'maya_window') and cmds.window(self.maya_window, exists=True):
			cmds.deleteUI(self.maya_window, window=True)
			print(f"Maya окно '{self.maya_window}' удалено")

		self.panel_name = None
		self.outliner_panel = None

	def _init_toolbar(self):
		"""
		Инициализация соответствующего тулбара в зависимости от типа панели.
		"""
		if self.panel_type == "reference":
			self.toolbar = ReferenceToolbar(parent=self)
			print(f"OutlinerPanel: Создан ReferenceToolbar для панели референсов")
		elif self.panel_type == "scene":
			self.toolbar = SceneToolbar(parent=self)
			print(f"OutlinerPanel: Создан SceneToolbar для панели сцены")

		# Подключаем сигналы тулбара к методам обработки
		if self.toolbar:
			self._connect_toolbar_signals()

	def _init_panel_menu(self):
		"""
		Инициализация соответствующего контекстного меню.
		"""
		if self.panel_type == "reference":
			self.panel_menu = ReferenceMenu(parent=self)
			print(f"OutlinerPanel: Создано ReferenceMenu для панели референсов")
		elif self.panel_type == "scene":
			self.panel_menu = SceneMenu(parent=self)
			print(f"OutlinerPanel: Создано SceneMenu для панели сцены")

		# Настраиваем связь меню с панелью
		if self.panel_menu:
			self.panel_menu.set_outliner_panel(self)

	def _init_panel_label(self):
		"""
		Установка заголовка панели в зависимости от типа.
		"""
		if self.panel_type == "reference":
			self.panel_label = QLabel("Reference")
		elif self.panel_type == "scene":
			self.panel_label = QLabel("Scene")

		if self.panel_label:
			self.panel_label.setFixedHeight(20)  # Фиксированная высота
			self.panel_label.setAlignment(Qt.AlignCenter)

	def _connect_toolbar_signals(self):
		"""
		Подключение сигналов тулбара к обработчикам (MVC Controller).
		"""
		if isinstance(self.toolbar, ReferenceToolbar):
			# Подключаем сигнал изменения режима переопределения
			# self.toolbar.override_mode_changed.connect(self._handle_override_mode_changed)
			# self.toolbar.override_mode_changed.connect(self.override_mode_changed.emit)

			pass
		elif isinstance(self.toolbar, SceneToolbar):
			# Подключаем другие сигналы SceneToolbar при необходимости
			# self.toolbar.validate_mode_changed.connect(self.validate_mode_changed.emit)
			pass
